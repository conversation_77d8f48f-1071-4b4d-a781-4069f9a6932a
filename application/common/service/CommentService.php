<?php

namespace app\common\service;

use app\common\model\Comment;
use app\common\model\CommentLike;

/**
 * 评论服务类
 * 统一处理评论相关的业务逻辑
 */
class CommentService
{
    /**
     * 验证评论数据
     * @param array $data
     * @return array|true
     */
    public static function validateCommentData($data)
    {
        // 验证必填字段
        if (empty($data['nickname'])) {
            return ['code' => 0, 'msg' => '昵称不能为空'];
        }
        
        if (empty($data['email'])) {
            return ['code' => 0, 'msg' => '邮箱不能为空'];
        }
        
        if (empty($data['content'])) {
            return ['code' => 0, 'msg' => '评论内容不能为空'];
        }
        
        // 验证邮箱格式
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return ['code' => 0, 'msg' => '邮箱格式不正确'];
        }
        
        // 验证内容长度
        if (mb_strlen($data['content']) > 1000) {
            return ['code' => 0, 'msg' => '评论内容不能超过1000字符'];
        }
        
        return true;
    }
    
    /**
     * 获取客户端信息
     * @param object $request
     * @return array
     */
    public static function getClientInfo($request)
    {
        $ip = $request->ip();
        $userAgent = $request->server('HTTP_USER_AGENT');
        
        // IP归属地
        $ip_location = self::getIpLocationByApi($ip);
        
        // 浏览器识别
        $browser = 'Unknown';
        if (preg_match('/(Chrome|Firefox|Safari|Edge|Opera|IE)/i', $userAgent, $match)) {
            $browser = $match[1];
        }
        
        // 设备识别
        $device = 'PC';
        if (preg_match('/mobile/i', $userAgent)) {
            $device = 'Mobile';
        } elseif (preg_match('/iPad|Tablet/i', $userAgent)) {
            $device = 'Tablet';
        }
        
        return [
            'ip' => $ip,
            'ip_location' => $ip_location,
            'browser' => $browser,
            'device' => $device
        ];
    }
    
    /**
     * 创建评论
     * @param array $data
     * @return bool
     */
    public static function createComment($data)
    {
        $comment = new Comment();
        return $comment->save($data);
    }
    
    /**
     * 点赞/取消点赞评论
     * @param int $commentId
     * @param string $ip
     * @return array
     */
    public static function toggleLike($commentId, $ip)
    {
        if (!$commentId) {
            return ['code' => 0, 'msg' => '参数错误'];
        }
        
        // 检查评论是否存在
        $comment = Comment::find($commentId);
        if (!$comment) {
            return ['code' => 0, 'msg' => '评论不存在'];
        }
        
        // 检查是否已经点赞
        $likeRecord = CommentLike::where([
            'comment_id' => $commentId,
            'ip' => $ip
        ])->find();
        
        if ($likeRecord) {
            // 取消点赞
            $likeRecord->delete();
            Comment::where('id', $commentId)->setDec('likes');
            return ['code' => 1, 'msg' => '取消点赞成功'];
        } else {
            // 添加点赞
            CommentLike::create([
                'comment_id' => $commentId,
                'ip' => $ip,
                'createtime' => time()
            ]);
            Comment::where('id', $commentId)->setInc('likes');
            return ['code' => 1, 'msg' => '点赞成功'];
        }
    }
    
    /**
     * 获取头像URL
     * @param string $email
     * @param string $nickname
     * @return string
     */
    public static function getAvatarUrl($email, $nickname)
    {
        $email = strtolower(trim($email));
        
        // QQ邮箱头像
        if (preg_match('/^([1-9][0-9]{4,10})@qq\.com$/', $email, $m)) {
            return 'https://q1.qlogo.cn/g?b=qq&nk=' . $m[1] . '&s=100';
        }
        
        // 生成字母头像
        $letter = strtoupper(mb_substr($nickname ?: $email, 0, 1, 'utf-8'));
        $hash = 0;
        for ($i = 0; $i < strlen($email); $i++) {
            $hash = ord($email[$i]) + (($hash << 5) - $hash);
        }
        
        $color = '#';
        for ($i = 0; $i < 3; $i++) {
            $value = ($hash >> ($i * 8)) & 0xFF;
            $color .= str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
        }
        
        $svg = '<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg">' .
               '<circle cx="24" cy="24" r="24" fill="' . $color . '"/>' .
               '<text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" ' .
               'font-family="Arial,Helvetica,sans-serif" dy=".1em">' . $letter . '</text></svg>';
               
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
    
    /**
     * 检查是否为管理员邮箱
     * @param string $email
     * @return bool
     */
    public static function isAdminEmail($email)
    {
        $adminEmails = ['<EMAIL>']; // 可配置的管理员邮箱列表
        return in_array(strtolower(trim($email)), $adminEmails);
    }
    
    /**
     * 格式化评论内容
     * @param string $content
     * @return string
     */
    public static function formatCommentContent($content)
    {
        // @高亮处理
        $content = preg_replace('/@([\x{4e00}-\x{9fa5}A-Za-z0-9_\-\.]+)：/u', '<span class="text-primary fw-bold">@$1：</span>', $content);
        return nl2br(htmlspecialchars($content));
    }
    
    /**
     * 通过第三方API获取IP归属地
     * @param string $ip
     * @return string
     */
    private static function getIpLocationByApi($ip)
    {
        $url = "http://ip-api.com/json/{$ip}?lang=zh-CN";
        $resp = @file_get_contents($url);
        if ($resp) {
            $data = json_decode($resp, true);
            if ($data && $data['status'] == 'success') {
                return $data['country'] . $data['regionName'] . $data['city'];
            }
        }
        return '';
    }
}
