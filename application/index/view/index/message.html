{extend name="layout/blog" /}

{block name="content"}
<div class="container">
    <div class="card">
        <div class="card-header">
            <i class="fa fa-comments me-1"></i> 留言板
        </div>
        <div class="card-body">
            <!-- 发表留言 -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-pen me-1"></i> 发表留言
                </div>
                <div class="card-body">
                    <form class="comment-form" method="post" action="/api/comment/add">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="nickname" class="form-label">昵称 <span class="text-danger">*</span></label>
                                <input type="text" id="nickname" name="nickname" class="form-control" placeholder="请输入昵称" required>
                            </div>
                            <div class="col-md-4">
                                <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" id="email" name="email" class="form-control" placeholder="请输入邮箱" required>
                            </div>
                            <div class="col-md-4">
                                <label for="website" class="form-label">网址（可选）</label>
                                <input type="url" id="website" name="website" class="form-control" placeholder="可选，带 http(s)://">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">评论内容 <span class="text-danger">*</span></label>
                            <textarea id="content" name="content" class="form-control" rows="4" placeholder="欢迎留言，分享你的想法..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary px-4">发表</button>
                    </form>
                </div>
            </div>

            <!-- 留言列表 -->
            <div class="card comment-section">
                <!-- <div class="card-header d-flex justify-content-between align-items-center">
                    <div><i class="fa fa-list me-2"></i>全部留言</div>
                    <div>
                        <a href="{$url}?sort=new" class="btn btn-sm {if $sort=='new'}btn-primary{else}btn-outline-primary{/if}">最新</a>
                        <a href="{$url}?sort=hot" class="btn btn-sm {if $sort=='hot'}btn-primary{else}btn-outline-primary{/if}">最热</a>
                    </div>
                </div> -->
                <!-- <div class="card-header"> -->
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-list me-1"></i> 全部留言
                            <span class="badge bg-primary ms-2">{$total}</span>
                        </div>
                        <div class="btn-group" role="group">
                            <a href="{$url}?sort=desc" class="btn btn-sm {if $sort=='desc'}btn-primary{else}btn-outline-primary{/if}">
                                <i class="fas fa-sort-amount-down me-1"></i> 最新
                            </a>
                            <a href="{$url}?sort=asc" class="btn btn-sm {if $sort=='asc'}btn-primary{else}btn-outline-primary{/if}">
                                <i class="fas fa-sort-amount-up me-1"></i> 最早
                            </a>
                            <a href="{$url}?sort=hot" class="btn btn-sm {if $sort=='hot'}btn-primary{else}btn-outline-primary{/if}">
                                <i class="fas fa-fire me-1"></i> 最热
                            </a>
                        </div>
                    </div>
                <!-- </div> -->
                <div class="card-body">
                    <div class="comment-list">
                        {empty name="commentList"}
                        <div class="text-center text-muted py-5">暂无留言，快来抢沙发吧！</div>
                        {else/}
                        {volist name="commentList" id="item"}
                        <div class="han-message-card">
                          <div class="han-message-header">
                            <img class="han-avatar" src="<?php
                              $email = isset($item['email']) ? strtolower(trim($item['email'])) : '';
                              $nickname = isset($item['nickname']) ? $item['nickname'] : '';
                              if (preg_match('/^([1-9][0-9]{4,10})@qq\.com$/', $email, $m)) {
                                echo 'https://q1.qlogo.cn/g?b=qq&nk=' . $m[1] . '&s=100';
                              } else {
                                $letter = strtoupper(mb_substr($nickname ?: $email, 0, 1, 'utf-8'));
                                $hash = 0;
                                for ($i = 0; $i < strlen($email); $i++) {
                                  $hash = ord($email[$i]) + (($hash << 5) - $hash);
                                }
                                $color = '#';
                                for ($i = 0; $i < 3; $i++) {
                                  $value = ($hash >> ($i * 8)) & 0xFF;
                                  $color .= str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
                                }
                                $svg = '<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="24" fill="' . $color . '"/><text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" font-family="Arial,Helvetica,sans-serif" dy=".1em">' . $letter . '</text></svg>';
                                echo 'data:image/svg+xml;base64,' . base64_encode($svg);
                              }
                            ?>" alt="<?php echo $item['nickname']; ?>" width="48" height="48" onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
                            <div class="han-meta">
                              <span class="han-nickname"><?php echo $item['nickname']; ?></span>
                              <?php if(isset($item['is_admin']) && $item['is_admin']): ?>
                                <span class="han-badge">博主</span>
                                <span class="han-cert"><i class="fa fa-check-circle"></i></span>
                              <?php endif; ?>
                              <span class="han-dot">·</span>
                              <span class="han-time"><i class="fa fa-calendar"></i> <?php echo date('Y-m-d', $item['createtime']); ?></span>
                              <?php if(!empty($item['ip_location'])): ?>
                                <span class="han-dot">·</span>
                                <span class="han-ip"><i class="fa fa-map-marker-alt"></i> <?php echo $item['ip_location']; ?></span>
                              <?php endif; ?>
                              <?php if(!empty($item['browser'])): ?>
                                <span class="han-dot">·</span>
                                <span class="han-browser"><i class="fa fa-globe"></i> <?php echo $item['browser']; ?></span>
                              <?php endif; ?>
                              <?php if(!empty($item['device'])): ?>
                                <span class="han-dot">·</span>
                                <span class="han-device"><i class="fa fa-desktop"></i> <?php echo $item['device']; ?></span>
                              <?php endif; ?>
                            </div>
                          </div>
                          <div class="han-message-content">
                            <?php 
                              $content = isset($item['content']) ? $item['content'] : '';
                              // @高亮处理
                              $content = preg_replace('/@([\x{4e00}-\x{9fa5}A-Za-z0-9_\-\.]+)：/u', '<span class="han-at">@$1：</span>', $content);
                              echo nl2br($content);
                            ?>
                          </div>
                          <div class="han-message-actions">
                            <form method="post" action="<?php echo url('index/index/message_like', ['id'=>$item['id']]); ?>" style="display:inline;">
                              <button type="submit" class="han-like"><i class="fa fa-heart"></i> <?php echo (isset($item['likes']) && ($item['likes'] !== '')?$item['likes']:0); ?></button>
                            </form>
                            <button class="han-reply"><i class="fa fa-comment-alt"></i> 回复</button>
                          </div>
                        </div>
                        {/volist}
                        {/empty}
                    </div>
                    <!-- 分页 -->
                    {include file="common/paginator" total="$total" limit="$limit" page="$page" url="$url" query="$query" /}
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 字符串转颜色
function stringToColor(str) {
    var hash = 0;
    for (var i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    var color = '#';
    for (var i = 0; i < 3; i++) {
        var value = (hash >> (i * 8)) & 0xFF;
        color += ('00' + value.toString(16)).substr(-2);
    }
    return color;
}
// 获取头像URL
function getAvatarUrl(email, nickname) {
    email = email.trim().toLowerCase();
    var qqMatch = email.match(/^([1-9][0-9]{4,10})@qq\.com$/);
    if (qqMatch) {
        var qq = qqMatch[1];
        return 'https://q1.qlogo.cn/g?b=qq&nk=' + qq + '&s=100';
    }
    var letter = (nickname || email).charAt(0).toUpperCase();
    var color = stringToColor(email);
    var svg = `<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="24" fill="${color}"/><text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" font-family="Arial,Helvetica,sans-serif" dy=".1em">${letter}</text></svg>`;
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
}
$(function(){
    function loadCachedUserInfo() {
        var cachedNickname = localStorage.getItem('message_nickname');
        var cachedEmail = localStorage.getItem('message_email');
        var cachedWebsite = localStorage.getItem('message_website');
        if (cachedNickname) {
            $('input[name="nickname"]').val(cachedNickname);
        }
        if (cachedEmail) {
            $('input[name="email"]').val(cachedEmail);
        }
        if (cachedWebsite) {
            $('input[name="website"]').val(cachedWebsite);
        }
    }
    function saveUserInfoToCache(nickname, email, website) {
        if (nickname && nickname.trim()) {
            localStorage.setItem('message_nickname', nickname.trim());
        }
        if (email && email.trim()) {
            localStorage.setItem('message_email', email.trim());
        }
        if (website && website.trim()) {
            localStorage.setItem('message_website', website.trim());
        }
    }
    loadCachedUserInfo();
    $('.comment-form').on('submit', function(e){
        e.preventDefault();
        var $form = $(this);
        var $btn = $form.find('button[type=submit]');
        var nickname = $form.find('input[name="nickname"]').val();
        var email = $form.find('input[name="email"]').val();
        var website = $form.find('input[name="website"]').val();
        var content = $form.find('textarea[name="content"]').val();
        $btn.prop('disabled', true);
        $.post($form.attr('action'), $form.serialize(), function(res){
            $btn.prop('disabled', false);
            if(res.code == 1){
                saveUserInfoToCache(nickname, email, website);
                var now = new Date();
                var timeStr = now.getFullYear() + '-' + 
                    ('0'+(now.getMonth()+1)).slice(-2) + '-' + 
                    ('0'+now.getDate()).slice(-2) + ' ' + 
                    ('0'+now.getHours()).slice(-2) + ':' + 
                    ('0'+now.getMinutes()).slice(-2);
                var avatarUrl = getAvatarUrl(email, nickname);
                var newComment = `
                    <div class="comment-item mb-4 d-flex">
                        <img src="${avatarUrl}" class="comment-avatar rounded-circle me-3" alt="${nickname}" width="48" height="48" onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">${$('<div>').text(nickname).html()}</span>
                                <span class="text-muted small">${timeStr}</span>
                            </div>
                            <div class="comment-content my-2">${$('<div>').text(content).html().replace(/\n/g,'<br>')}</div>
                        </div>
                    </div>
                `;
                $('.comment-list').prepend(newComment);
                $form[0].reset();
                loadCachedUserInfo();
            }else{
                alert(res.msg || '留言失败！');
            }
        }, 'json').fail(function(){
            $btn.prop('disabled', false);
            alert('网络错误，请稍后重试！');
        });
    });
});
</script>
<script>console.log("留言页面测试");</script>
{/block} 

<style>
body {
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
  background: #f6f8fa !important;
}
.han-message-card {
  background: #fff !important;
  border-radius: 18px !important;
  box-shadow: 0 4px 24px 0 rgba(37,99,235,0.08) !important;
  margin-bottom: 24px !important;
  padding: 24px 28px 18px 28px !important;
  position: relative !important;
  border: none !important;
}
.han-message-header {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 8px !important;
}
.han-avatar {
  width: 48px !important; height: 48px !important; border-radius: 50% !important; margin-right: 16px !important; border: 2px solid #fff !important; box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
}
.han-meta { display: flex !important; align-items: center !important; flex-wrap: wrap !important; font-size: 1.05em !important; color: #555 !important; }
.han-nickname { font-weight: 600 !important; color: #222 !important; margin-right: 8px !important; font-size: 1.1em !important; }
.han-badge { background: linear-gradient(90deg,#2563eb,#1e40af) !important; color: #fff !important; border-radius: 8px !important; padding: 2px 10px !important; font-size: 0.95em !important; margin-right: 6px !important; }
.han-cert { color: #22c55e !important; font-size: 1.1em !important; margin-right: 8px !important; }
.han-dot { color: #bbb !important; margin: 0 6px !important; }
.han-time, .han-ip, .han-browser, .han-device { color: #888 !important; font-size: 0.98em !important; }
.han-message-content { font-size: 1.18em !important; color: #222 !important; margin: 12px 0 16px 0 !important; line-height: 1.7 !important; }
.han-at { color: #2563eb !important; font-weight: 600 !important; }
.han-message-actions { display: flex !important; gap: 16px !important; }
.han-like, .han-reply {
  background: linear-gradient(90deg,#f3f6fa 60%,#e0e7ff 100%) !important;
  border: none !important;
  border-radius: 999px !important;
  padding: 6px 22px !important;
  font-size: 1.05em !important;
  color: #2563eb !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.10) !important;
  transition: background .2s, color .2s, box-shadow .2s !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  outline: none !important;
}
.han-like:hover, .han-reply:hover {
  background: linear-gradient(90deg,#e0e7ff 60%,#c7d2fe 100%) !important;
  color: #1e40af !important;
  box-shadow: 0 4px 16px 0 rgba(37,99,235,0.13) !important;
}
.han-like .fa-heart { color: #e0245e !important; }
.han-reply .fa-comment-alt { color: #2563eb !important; }
.han-like, .han-reply, .han-like:active, .han-reply:active, .han-like:focus, .han-reply:focus {
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.10) !important;
  border: none !important;
  outline: none !important;
}
</style> 
