{extend name="layout/blog" /}

{block name="content"}
<div class="container">
    <div class="card">
        <div class="card-header">
            <i class="fa fa-comments me-1"></i> 留言板
        </div>
        <div class="card-body">
            <!-- 发表留言 -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fa fa-pen me-1"></i> 发表留言
                </div>
                <div class="card-body">
                    <form class="comment-form" method="post" action="/api/comment/add"
                        <input type="hidden" name="type" value="message">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="nickname" class="form-label">昵称 <span class="text-danger">*</span></label>
                                <input type="text" id="nickname" name="nickname" class="form-control" placeholder="请输入昵称" required>
                            </div>
                            <div class="col-md-4">
                                <label for="email" class="form-label">邮箱 <span class="text-danger">*</span></label>
                                <input type="email" id="email" name="email" class="form-control" placeholder="请输入邮箱" required>
                            </div>
                            <div class="col-md-4">
                                <label for="website" class="form-label">网址（可选）</label>
                                <input type="url" id="website" name="website" class="form-control" placeholder="可选，带 http(s)://">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">留言内容 <span class="text-danger">*</span></label>
                            <textarea id="content" name="content" class="form-control" rows="4" placeholder="欢迎留言，分享你的想法..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary px-4">发表留言</button>
                    </form>
                </div>
            </div>

            <!-- 留言列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fa fa-list me-1"></i> 全部留言
                        <span class="badge bg-primary ms-2">{$total}</span>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="{$url}?sort=desc" class="btn btn-sm {if $sort=='desc'}btn-primary{else}btn-outline-primary{/if}">
                            <i class="fa fa-sort-amount-down me-1"></i> 最新
                        </a>
                        <a href="{$url}?sort=asc" class="btn btn-sm {if $sort=='asc'}btn-primary{else}btn-outline-primary{/if}">
                            <i class="fa fa-sort-amount-up me-1"></i> 最早
                        </a>
                        <a href="{$url}?sort=hot" class="btn btn-sm {if $sort=='hot'}btn-primary{else}btn-outline-primary{/if}">
                            <i class="fa fa-fire me-1"></i> 最热
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="comment-list">
                        {empty name="commentList"}
                        <div class="text-center text-muted py-5">
                            <i class="fa fa-comments fa-3x mb-3"></i>
                            <p>暂无留言，快来抢沙发吧！</p>
                        </div>
                        {else/}
                        {volist name="commentList" id="item"}
                        <div class="message-item mb-4">
                            <div class="d-flex">
                                <img src="{$item.avatar_url|default='/assets/img/default-avatar.jpeg'}"
                                     class="message-avatar rounded-circle me-3"
                                     alt="{$item.nickname}"
                                     width="48" height="48"
                                     onerror="this.onerror=null;this.src='/assets/img/default-avatar.jpeg';">
                                <div class="flex-grow-1">
                                    <div class="message-header d-flex justify-content-between align-items-start mb-2">
                                        <div class="message-meta">
                                            <span class="fw-bold text-primary">{$item.nickname}</span>
                                            {if isset($item.is_admin) && $item.is_admin}
                                            <span class="badge bg-warning text-dark ms-1">博主</span>
                                            {/if}
                                            <span class="text-muted ms-2">
                                                <i class="fa fa-calendar me-1"></i>
                                                {$item.createtime|date='Y-m-d H:i'}
                                            </span>
                                            {if !empty($item.ip_location)}
                                            <span class="text-muted ms-2">
                                                <i class="fa fa-map-marker-alt me-1"></i>
                                                {$item.ip_location}
                                            </span>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="message-content mb-3">
                                        {$item.content|nl2br|raw}
                                    </div>
                                    <div class="message-actions">
                                        <form method="post" action="{:url('message/like', ['id'=>$item.id])}" style="display:inline;">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fa fa-heart me-1"></i>
                                                {$item.likes|default=0}
                                            </button>
                                        </form>
                                        <button class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fa fa-reply me-1"></i>
                                            回复
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/volist}
                        {/empty}
                    </div>
                    <!-- 分页 -->
                    {include file="common/paginator" total="$total" limit="$limit" page="$page" url="$url" query="$query" /}
                </div>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<script>
// 工具函数：字符串转颜色
function stringToColor(str) {
    var hash = 0;
    for (var i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    var color = '#';
    for (var i = 0; i < 3; i++) {
        var value = (hash >> (i * 8)) & 0xFF;
        color += ('00' + value.toString(16)).substr(-2);
    }
    return color;
}

// 工具函数：获取头像URL
function getAvatarUrl(email, nickname) {
    email = email.trim().toLowerCase();
    var qqMatch = email.match(/^([1-9][0-9]{4,10})@qq\.com$/);
    if (qqMatch) {
        var qq = qqMatch[1];
        return 'https://q1.qlogo.cn/g?b=qq&nk=' + qq + '&s=100';
    }
    var letter = (nickname || email).charAt(0).toUpperCase();
    var color = stringToColor(email);
    var svg = `<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="24" fill="${color}"/><text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" font-family="Arial,Helvetica,sans-serif" dy=".1em">${letter}</text></svg>`;
    return 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svg)));
}
// 页面初始化
$(function(){
    // 加载缓存的用户信息
    function loadCachedUserInfo() {
        var cachedNickname = localStorage.getItem('message_nickname');
        var cachedEmail = localStorage.getItem('message_email');
        var cachedWebsite = localStorage.getItem('message_website');

        if (cachedNickname) {
            $('input[name="nickname"]').val(cachedNickname);
        }
        if (cachedEmail) {
            $('input[name="email"]').val(cachedEmail);
        }
        if (cachedWebsite) {
            $('input[name="website"]').val(cachedWebsite);
        }
    }

    // 保存用户信息到缓存
    function saveUserInfoToCache(nickname, email, website) {
        if (nickname && nickname.trim()) {
            localStorage.setItem('message_nickname', nickname.trim());
        }
        if (email && email.trim()) {
            localStorage.setItem('message_email', email.trim());
        }
        if (website && website.trim()) {
            localStorage.setItem('message_website', website.trim());
        }
    }

    // 页面加载时恢复用户信息
    loadCachedUserInfo();
    // 留言表单提交处理
    $('.comment-form').on('submit', function(e){
        e.preventDefault();

        var $form = $(this);
        var $btn = $form.find('button[type=submit]');
        var nickname = $form.find('input[name="nickname"]').val();
        var email = $form.find('input[name="email"]').val();
        var website = $form.find('input[name="website"]').val();
        var content = $form.find('textarea[name="content"]').val();

        // 禁用提交按钮
        $btn.prop('disabled', true).text('提交中...');

        // 发送AJAX请求
        $.post($form.attr('action'), $form.serialize(), function(res){
            $btn.prop('disabled', false).text('发表留言');

            if(res.code == 1){
                // 保存用户信息到缓存
                saveUserInfoToCache(nickname, email, website);

                // 显示成功消息
                alert('留言发表成功！');

                // 清空表单并重新加载页面
                $form[0].reset();
                loadCachedUserInfo();

                // 刷新页面显示新留言
                setTimeout(function(){
                    window.location.reload();
                }, 1000);
            } else {
                alert(res.msg || '留言失败！');
            }
        }, 'json').fail(function(){
            $btn.prop('disabled', false).text('发表留言');
            alert('网络错误，请稍后重试！');
        });
    });
});
</script>
{/block}
