<?php

use think\Route;

// 博客首页
Route::get('/', 'index/Index/index');

// 文章详情
Route::get('article/:id', 'index/Index/detail');

// 归档页面
Route::get('archive', 'index/Index/archive');

// 分类页面
Route::get('category', 'index/Index/category');
Route::get('category/:id', 'index/Index/category');

// 标签页面
Route::get('tag', 'index/Index/tag');
Route::get('tag/:id', 'index/Index/tag');

// 留言页面
Route::get('message', 'index/Index/message');

// 关于页面
Route::get('about', 'index/Index/about');

// 友链页面
Route::get('links', 'index/Index/links');

// 评论相关
Route::post('comment/add', 'index/Comment/add');
Route::get('comment/list', 'index/Comment/getList');
Route::post('comment/like', 'index/Comment/like');
Route::post('comment/delete', 'index/Comment/delete');
Route::post('comment/reply', 'index/Comment/reply');

// 留言点赞
Route::post('message/like/:id', 'index/Index/message_like');