<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Article;
use app\common\model\BlogCategory;
use app\common\model\Tag;
use app\common\model\Comment;
use think\Db;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    /**
     * Article模型对象
     * @var \app\common\model\Article
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new Article;
    }

    /**
     * 首页
     */
    public function index()
    {
        $page = $this->request->param('page', 1);
        file_put_contents(ROOT_PATH . 'runtime/page_debug.log', "page=" . var_export($page, true) . PHP_EOL, FILE_APPEND);
        $limit = 10;

        // 获取文章列表，包含分类信息
        $articleList = $this->model
            ->alias('a')
            ->join('blog_category c', 'a.category_id = c.id', 'left')
            ->where('a.status', 'normal')
            ->field('a.*, c.name as category_name')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();

        // 为每篇文章获取标签信息
        foreach ($articleList as &$article) {
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }

        $blogger = config('site.blogger') ?: config('blogger');
        $site = array_merge(
            config('site'),
            [
                'article_count' => $this->model->where('status', 'normal')->count(),
                'category_count' => BlogCategory::where('status', 'normal')->count(),
                'tag_count' => Tag::where('status', 'normal')->count(),
            ]
        );

        $hotCategories = $this->getHotCategories();
        if (count($hotCategories) > 10) $hotCategories = array_slice($hotCategories, 0, 10);
        $hotTags = $this->getHotTags();
        if (count($hotTags) > 10) $hotTags = array_slice($hotTags, 0, 10);
        $recommendArticles = $this->getRecommendArticles();
        if (is_object($recommendArticles)) {
            $recommendArticles = $recommendArticles->toArray();
        }
        if (count($recommendArticles) < 5) {
            $ids = array_column($recommendArticles, 'id');
            $commentArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->order('comment_count desc')->limit(5 - count($recommendArticles))->select()->toArray();
            $recommendArticles = array_merge($recommendArticles, $commentArticles);
            $ids = array_column($recommendArticles, 'id');
            if (count($recommendArticles) < 5) {
                $likeArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->order('likes desc')->limit(5 - count($recommendArticles))->select()->toArray();
                $recommendArticles = array_merge($recommendArticles, $likeArticles);
                $ids = array_column($recommendArticles, 'id');
            }
            if (count($recommendArticles) < 5) {
                $newArticles = $this->model->where('status', 'normal')->whereNotIn('id', $ids)->order('createtime desc')->limit(5 - count($recommendArticles))->select()->toArray();
                $recommendArticles = array_merge($recommendArticles, $newArticles);
            }
        }

        $adSidebarTop = config('site.ads.sidebar_top') ?: ['enable' => false, 'content' => ''];
        $adSidebarBottom = config('site.ads.sidebar_bottom') ?: ['enable' => false, 'content' => ''];
        $this->view->assign([
            'articleList' => $articleList,
            'page' => $page,
            'total' => $site['article_count'],
            'limit' => $limit,
            'url' => url('index/index/index'),
            'query' => '',
            'site' => $site,
            'hotCategories' => $hotCategories,
            'hotTags' => $hotTags,
            'recommendArticles' => $recommendArticles,
            'adSidebarTop' => $adSidebarTop,
            'adSidebarBottom' => $adSidebarBottom,
        ]);

        return $this->view->fetch();
    }

    /**
     * 获取博主信息
     */
    private function getBloggerInfo()
    {
        $articleCount = $this->model->where('status', 'normal')->count();
        $categoryCount = BlogCategory::where('status', 'normal')->count();
        $tagCount = Tag::where('status', 'normal')->count();

        return [
            'name' => '德胜独立开发',
            'avatar' => '/assets/img/avatar.jpg',
            'signature' => '热爱技术，专注独立开发',
            'profession' => '全栈开发工程师',
            'sideBusiness' => '技术博主',
            'announcement' => '欢迎来到我的博客，分享技术心得和独立开发经验！',
            'articleCount' => $articleCount,
            'categoryCount' => $categoryCount,
            'tagCount' => $tagCount
        ];
    }

    /**
     * 获取热门分类
     */
    private function getHotCategories()
    {
        return BlogCategory::alias('c')
            ->join('article a', 'c.id = a.category_id')
            ->where('a.status', 'normal')
            ->where('c.status', 'normal')
            ->field('c.id, c.name, count(a.id) as count')
            ->group('c.id')
            ->order('count desc')
            ->limit(4)
            ->select();
    }

    /**
     * 获取热门标签
     */
    private function getHotTags()
    {
        return Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->join('article a', 'at.article_id = a.id')
            ->where('a.status', 'normal')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(at.article_id) as count')
            ->group('t.id')
            ->order('count desc')
            ->limit(18)
            ->select();
    }

    /**
     * 获取推荐文章
     */
    private function getRecommendArticles()
    {
        return $this->model
            ->where('status', 'normal')
            ->where('is_recommend', 1)
            ->order('createtime desc')
            ->limit(5)
            ->select();
    }

    /**
     * 文章详情
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        $article = $this->model->find($id);
        if (!$article) {
            $this->error('文章不存在');
        }

        // 获取评论
        $comments = Comment::where('article_id', $id)
            ->where('status', 'normal')
            ->order('createtime desc')
            ->select();

        // 增加阅读量
        $this->model->where('id', $id)->setInc('views');

        $this->view->assign([
            'article' => $article,
            'comments' => $comments
        ]);

        return $this->view->fetch();
    }

    /**
     * 归档页面
     */
    public function archive()
    {
        // 查询所有年份及每年文章总数
        $years = $this->model
            ->where('status', 'normal')
            ->field('YEAR(FROM_UNIXTIME(createtime)) as year, COUNT(*) as count')
            ->group('year')
            ->order('year desc')
            ->select();
        $total = $this->model->where('status', 'normal')->count();
        $this->view->assign([
            'archives' => $years,
            'total' => $total,
            'limit' => 10,
            'page' => 1,
            'url' => url('index/index/archive'),
            'query' => ''
        ]);
        return $this->view->fetch();
    }

    /**
     * 归档年份文章列表
     */
    public function archive_list($year = null)
    {
        if (!$year) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->where('status', 'normal')
            ->whereTime('createtime', 'between', [strtotime("$year-01-01 00:00:00"), strtotime("$year-12-31 23:59:59")])
            ->order('createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->where('status', 'normal')
            ->whereTime('createtime', 'between', [strtotime("$year-01-01 00:00:00"), strtotime("$year-12-31 23:59:59")])
            ->count();
        // 为每篇文章补充分类和标签信息
        foreach ($articles as &$article) {
            $category = BlogCategory::where('id', $article['category_id'])->find();
            $article['category_name'] = $category ? $category['name'] : '未分类';
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }
        $this->view->assign([
            'year' => $year,
            'articles' => $articles,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('index/index/archive_list', ['year'=>$year]),
            'query' => ''
        ]);
        return $this->view->fetch('archive_list');
    }

    /**
     * 分类页面
     */
    public function category($id = null)
    {
        if ($id) {
            $articles = $this->model
                ->where('category_id', $id)
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        } else {
            $articles = $this->model
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        }

        // 获取所有分类及每个分类下的文章数
        $categories = BlogCategory::where('status', 'normal')->order('sort desc,id desc')->select();
        foreach ($categories as &$cat) {
            $cat['article_count'] = $this->model->where('category_id', $cat['id'])->where('status', 'normal')->count();
        }
        $this->view->assign([
            'articles' => $articles,
            'categories' => $categories,
            'currentCategory' => $id
        ]);

        return $this->view->fetch();
    }

    /**
     * 标签页面
     */
    public function tag($id = null)
    {
        if ($id) {
            $articles = $this->model
                ->alias('a')
                ->join('article_tag at', 'a.id = at.article_id')
                ->where('at.tag_id', $id)
                ->where('a.status', 'normal')
                ->order('a.createtime desc')
                ->select();
        } else {
            $articles = $this->model
                ->where('status', 'normal')
                ->order('createtime desc')
                ->select();
        }

        // 获取所有标签及其文章数，按count降序
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id', 'left')
            ->join('article a', 'at.article_id = a.id', 'left')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(a.id) as count')
            ->group('t.id')
            ->order('count desc')
            ->select();
        // 计算最大最小count
        $tags = $tags ? (is_object($tags) ? $tags->toArray() : $tags) : [];
        $minFont = 1.0;
        $maxFont = 2.2;
        $minCount = null;
        $maxCount = null;
        foreach ($tags as $tag) {
            if ($minCount === null || $tag['count'] < $minCount) $minCount = $tag['count'];
            if ($maxCount === null || $tag['count'] > $maxCount) $maxCount = $tag['count'];
        }
        $range = $maxCount - $minCount > 0 ? $maxCount - $minCount : 1;
        foreach ($tags as &$tag) {
            $size = $minFont + ($tag['count'] - $minCount) / $range * ($maxFont - $minFont);
            $size = max($minFont, min($size, $maxFont));
            $color = ($tag['count'] == $maxCount) ? '#007bff' : (($tag['count'] == $minCount) ? '#6c757d' : '#17a2b8');
            $tag['font_size'] = $size;
            $tag['color'] = $color;
        }
        // 打乱标签顺序
        shuffle($tags);
        $this->view->assign([
            'articles' => $articles,
            'tags' => $tags,
            'currentTag' => $id
        ]);

        return $this->view->fetch();
    }

    /**
     * 标签文章列表页面
     */
    public function tag_list($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->alias('a')
            ->join('article_tag at', 'a.id = at.article_id')
            ->where('at.tag_id', $id)
            ->where('a.status', 'normal')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->alias('a')
            ->join('article_tag at', 'a.id = at.article_id')
            ->where('at.tag_id', $id)
            ->where('a.status', 'normal')
            ->count();
        $tag = Tag::where('id', $id)->find();
        // 标签云数据
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id', 'left')
            ->join('article a', 'at.article_id = a.id', 'left')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(a.id) as count')
            ->group('t.id')
            ->select();
        // 计算最大最小count
        $tags = $tags ? (is_object($tags) ? $tags->toArray() : $tags) : [];
        $minFont = 1.0;
        $maxFont = 2.2;
        $minCount = null;
        $maxCount = null;
        foreach ($tags as $t) {
            if ($minCount === null || $t['count'] < $minCount) $minCount = $t['count'];
            if ($maxCount === null || $t['count'] > $maxCount) $maxCount = $t['count'];
        }
        $range = $maxCount - $minCount > 0 ? $maxCount - $minCount : 1;
        foreach ($tags as &$t) {
            $size = $minFont + ($t['count'] - $minCount) / $range * ($maxFont - $minFont);
            $size = max($minFont, min($size, $maxFont));
            $color = ($t['count'] == $maxCount) ? '#007bff' : (($t['count'] == $minCount) ? '#6c757d' : '#17a2b8');
            $t['font_size'] = $size;
            $t['color'] = $color;
        }
        shuffle($tags);
        // 为每篇文章补充分类名
        foreach ($articles as &$article) {
            $category = BlogCategory::where('id', $article['category_id'])->find();
            $article['category_name'] = $category ? $category['name'] : '未分类';
        }
        $this->view->assign([
            'articles' => $articles,
            'tag' => $tag,
            'tags' => $tags,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('index/index/tag_list', ['id'=>$id]),
            'query' => ''
        ]);
        return $this->view->fetch('tag_list');
    }

    /**
 * 留言页面（后端渲染分页、排序、提交、点赞）
 */
public function message()
{
    $page = $this->request->param('page', 1);
    $limit = 10;
    $sort = $this->request->param('sort', 'desc'); // desc/asc/hot
   
    // 排序逻辑
    switch ($sort) {
        case 'asc':
            $order = 'createtime asc'; // 正序：最早在前
            break;
        case 'hot':
            $order = 'likes desc, createtime desc'; // 按热度：点赞数降序
            break;
        default:
            $order = 'createtime desc'; // 默认倒序：最新在前
    }
   
    $commentModel = new \app\common\model\Comment;
    $where = [
        'type' => 'message',
        'status' => 'normal'
    ];
    $total = $commentModel->where($where)->count();
    $commentList = $commentModel
        ->where($where)
        ->order($order)
        ->page($page, $limit)
        ->field('id, nickname, email, website, content, createtime, likes, ip_location, browser, device')
        ->select();
    // 补充is_admin字段，博主邮箱为*****************
    foreach ($commentList as &$item) {
        $item['is_admin'] = (isset($item['email']) && strtolower($item['email']) == '<EMAIL>') ? 1 : 0;
    }
    unset($item);
    
    // 处理留言提交
    if ($this->request->isPost()) {
        $data = $this->request->post();
        $data['type'] = 'message';
        $data['status'] = 'normal';
        $data['createtime'] = time();
        
        // 保存用户信息到cookie，方便下次使用
        cookie('message_nickname', $data['nickname'], 30*86400);
        cookie('message_email', $data['email'], 30*86400);
        if (!empty($data['website'])) {
            cookie('message_website', $data['website'], 30*86400);
        }
        
        if ($commentModel->save($data)) {
            $this->success('留言成功', url('index/index/message', ['sort' => $sort]));
        } else {
            $this->error('留言失败');
        }
    }
    
    // 从cookie中获取用户信息
    $this->view->assign([
        'commentList' => $commentList,
        'page' => $page,
        'total' => $total,
        'limit' => $limit,
        'sort' => $sort,
        'url' => url('index/index/message'),
        'query' => '',
        'saved_nickname' => cookie('message_nickname'),
        'saved_email' => cookie('message_email'),
        'saved_website' => cookie('message_website')
    ]);
    
    return $this->view->fetch();
}

    /**
     * 留言点赞
     */
    public function message_like($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        $comment = \app\common\model\Comment::get($id);
        if (!$comment) {
            $this->error('留言不存在');
        }
        $comment->setInc('likes');
        $this->success('点赞成功', url('index/index/message', $this->request->get()));
    }

    /**
     * 关于页面
     */
    public function about()
    {
        return $this->view->fetch();
    }

    /**
 * 获取头像URL
 */
public function get_avatar_url($email, $nickname)
{
    $email = strtolower(trim($email));
    if (preg_match('/^([1-9][0-9]{4,10})@qq\.com$/', $email, $m)) {
        return 'https://q1.qlogo.cn/g?b=qq&nk=' . $m[1] . '&s=100';
    } else {
        $letter = strtoupper(mb_substr($nickname ?: $email, 0, 1, 'utf-8'));
        $hash = 0;
        for ($i = 0; $i < strlen($email); $i++) {
            $hash = ord($email[$i]) + (($hash << 5) - $hash);
        }
        $color = '#';
        for ($i = 0; $i < 3; $i++) {
            $value = ($hash >> ($i * 8)) & 0xFF;
            $color .= str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
        }
        $svg = '<svg width="48" height="48" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="24" fill="' . $color . '"/><text x="50%" y="58%" text-anchor="middle" font-size="24" fill="#fff" font-family="Arial,Helvetica,sans-serif" dy=".1em">' . $letter . '</text></svg>';
        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }
}



/**
 * 格式化评论内容
 */
public function format_comment_content($content)
{
    // @高亮处理
    $content = preg_replace('/@([\x{4e00}-\x{9fa5}A-Za-z0-9_\-\.]+)：/u', '<span class="han-at">@$1：</span>', $content);
    return nl2br($content);
}

    /**
     * 友链页面
     */
    public function links()
    {
        $linkList = \think\Db::name('links')->where('status', 1)->order('id desc')->select();
        $this->assign('linkList', $linkList);
        return $this->view->fetch();
    }

    /**
     * Banner数据
     */
    public function bannerData()
    {
        return [
            'title' => '德胜独立开发',
            'subtitle' => '热爱技术，专注独立开发',
            'description' => '分享技术心得和独立开发经验',
            'image' => '/assets/img/banner.jpg'
        ];
    }

    /**
     * 标签云数据
     */
    public function tagcloud()
    {
        $tags = Tag::alias('t')
            ->join('article_tag at', 't.id = at.tag_id')
            ->join('article a', 'at.article_id = a.id')
            ->where('a.status', 'normal')
            ->where('t.status', 'normal')
            ->field('t.id, t.name, count(at.article_id) as count')
            ->group('t.id')
            ->order('count desc')
            ->select();

        return json($tags);
    }

    /**
     * 分类文章列表页面
     */
    public function category_list($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        $page = $this->request->param('page', 1);
        $limit = 10;
        $articles = $this->model
            ->alias('a')
            ->where('a.category_id', $id)
            ->where('a.status', 'normal')
            ->order('a.createtime desc')
            ->page($page, $limit)
            ->select();
        $total = $this->model
            ->alias('a')
            ->where('a.category_id', $id)
            ->where('a.status', 'normal')
            ->count();
        $category = BlogCategory::where('id', $id)->find();
        // 为每篇文章补充标签信息
        foreach ($articles as &$article) {
            $article['category_name'] = $category ? $category['name'] : '未分类';
            $tags = Tag::alias('t')
                ->join('article_tag at', 't.id = at.tag_id')
                ->where('at.article_id', $article['id'])
                ->where('t.status', 'normal')
                ->field('t.id, t.name')
                ->select();
            $article['tags'] = $tags;
        }
        $this->view->assign([
            'articles' => $articles,
            'category' => $category,
            'page' => $page,
            'total' => $total,
            'limit' => $limit,
            'url' => url('index/index/category_list', ['id'=>$id]),
            'query' => ''
        ]);
        return $this->view->fetch('category_list');
    }
}
