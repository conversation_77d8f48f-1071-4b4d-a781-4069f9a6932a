<?php
namespace app\api\controller;

use think\Controller;
use app\common\model\CommentLike;

class Comment extends Controller
{
    public function add()
    {
        try {
            if ($this->request->isPost()) {
                $params = $this->request->post();
                
                // 验证必填字段
                if (empty($params['nickname']) || empty($params['email']) || empty($params['content'])) {
                    return json(['code' => 0, 'msg' => '昵称、邮箱和内容不能为空']);
                }
                
                // 验证邮箱格式
                if (!filter_var($params['email'], FILTER_VALIDATE_EMAIL)) {
                    return json(['code' => 0, 'msg' => '邮箱格式不正确']);
                }
                
                $ip = $this->request->ip();
                
                // IP归属地自动识别
                $ip_location = '';
                if (class_exists('fast\Ip')) {
                    try {
                        $ip_location = \fast\Ip::find($ip);
                    } catch (\Exception $e) {
                        $ip_location = '';
                    }
                }
                if (!$ip_location) {
                    $ip_location = $this->getIpLocationByApi($ip);
                }
                
                // 后端自动识别浏览器和设备类型
                $userAgent = $this->request->server('HTTP_USER_AGENT');
                $browser = 'Unknown';
                $device = 'Unknown';
                
                // 浏览器识别
                if (preg_match('/(MSIE|Trident|Edge|Edg|Chrome|CriOS|Firefox|FxiOS|Safari|Opera|OPR|UCBrowser|QQBrowser|MicroMessenger|WeChat|QQ)/i', $userAgent, $bMatch)) {
                    $browserName = $bMatch[1];
                    if (preg_match('/' . $browserName . '[\/ ]([\d.]+)/i', $userAgent, $vMatch)) {
                        $browser = $browserName . ' ' . $vMatch[1];
                    } else {
                        $browser = $browserName;
                    }
                }
                
                // 操作系统识别
                if (preg_match('/(Windows NT [\d.]+|Mac OS X [\d_\.]+|Android [\d.]+|iPhone OS [\d_]+|iPad; CPU OS [\d_]+|Linux|Ubuntu|CrOS)/i', $userAgent, $osMatch)) {
                    $os = $osMatch[1];
                    $os = str_replace(['_', 'iPhone OS', 'CPU OS'], ['.', 'iOS', 'iOS'], $os);
                    $device = $os;
                }
                
                $data = [
                    'content' => trim($params['content']),
                    'nickname' => trim($params['nickname']),
                    'email' => trim($params['email']),
                    'website' => isset($params['website']) ? trim($params['website']) : '',
                    'type' => isset($params['type']) ? $params['type'] : 'message',
                    'article_id' => isset($params['article_id']) ? intval($params['article_id']) : 0,
                    'parent_id' => isset($params['parent_id']) ? intval($params['parent_id']) : 0,
                    'browser' => $browser,
                    'device' => $device,
                    'ip' => $ip,
                    'ip_location' => $ip_location,
                    'status' => 'normal',
                    'createtime' => time()
                ];
                
                $model = new \app\common\model\Comment();
                $result = $model->save($data);
                
                if ($result) {
                    return json(['code' => 1, 'msg' => '评论发布成功']);
                } else {
                    return json(['code' => 0, 'msg' => '评论发布失败']);
                }
            } else {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }
        } catch (\Throwable $e) {
            return json(['code' => 0, 'msg' => '异常: ' . $e->getMessage() . ' @' . $e->getFile() . ':' . $e->getLine()]);
        }
    }
    /**
     * 点赞评论
     */
    public function like()
    {
        if ($this->request->isPost()) {
            $commentId = $this->request->post('comment_id');
            $ip = $this->request->ip();
            $commentModel = new Comment();
            // 检查是否已经点赞
            $likeRecord = CommentLike::where([
                'comment_id' => $commentId,
                'ip' => $ip
            ])->find();
            if ($likeRecord) {
                // 取消点赞
                $likeRecord->delete();
                $commentModel->where('id', $commentId)->setDec('likes');
                return json(['code' => 1, 'msg' => '取消点赞成功']);
            } else {
                // 添加点赞
                CommentLike::create([
                    'comment_id' => $commentId,
                    'ip' => $ip,
                    'createtime' => time()
                ]);
                $commentModel->where('id', $commentId)->setInc('likes');
                return json(['code' => 1, 'msg' => '点赞成功']);
            }
        } else {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
    }
    /**
     * 通过第三方API获取IP归属地
     */
    protected function getIpLocationByApi($ip)
    {
        $url = "http://ip-api.com/json/{$ip}?lang=zh-CN";
        $resp = @file_get_contents($url);
        if ($resp) {
            $data = json_decode($resp, true);
            if ($data && $data['status'] == 'success') {
                return $data['country'] . $data['regionName'] . $data['city'];
            }
        }
        return '';
    }
} 