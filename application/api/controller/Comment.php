<?php
namespace app\api\controller;

use think\Controller;
use app\common\service\CommentService;

class Comment extends Controller
{
    /**
     * 添加评论/留言
     */
    public function add()
    {
        try {
            if (!$this->request->isPost()) {
                return $this->error('请求方式错误');
            }

            $params = $this->request->post();

            // 验证评论数据
            $validation = CommentService::validateCommentData($params);
            if ($validation !== true) {
                return json($validation);
            }

            // 获取客户端信息
            $clientInfo = CommentService::getClientInfo($this->request);

            // 构建数据
            $data = [
                'content' => trim($params['content']),
                'nickname' => trim($params['nickname']),
                'email' => trim($params['email']),
                'website' => isset($params['website']) ? trim($params['website']) : '',
                'type' => isset($params['type']) ? $params['type'] : 'message',
                'article_id' => isset($params['article_id']) ? intval($params['article_id']) : 0,
                'parent_id' => isset($params['parent_id']) ? intval($params['parent_id']) : 0,
                'browser' => $clientInfo['browser'],
                'device' => $clientInfo['device'],
                'ip' => $clientInfo['ip'],
                'ip_location' => $clientInfo['ip_location'],
                'status' => 'normal',
                'createtime' => time()
            ];

            $result = CommentService::createComment($data);

            if ($result) {
                return json(['code' => 1, 'msg' => '评论发布成功']);
            } else {
                return json(['code' => 0, 'msg' => '评论发布失败']);
            }
        } catch (\Throwable $e) {
            return json(['code' => 0, 'msg' => '系统异常：' . $e->getMessage()]);
        }
    }
    /**
     * 点赞评论
     */
    public function like()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        $commentId = $this->request->post('comment_id');
        $ip = $this->request->ip();

        $result = CommentService::toggleLike($commentId, $ip);
        return json($result);
    }
}